PS D:\project\ai-dance\legend_dance> flutter run -d FMR0224924017063
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on ALN AL00 in debug mode...
Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Running Gradle task 'assembleDebug'...                             13.6s
√ Built build\app\outputs\flutter-apk\app-debug.apk
Installing build\app\outputs\flutter-apk\app-debug.apk...          12.5s
I/flutter (21210): [INFO:flutter/shell/platform/android/android_context_vk_impeller.cc(65)] Known bad Vulkan driver encountered, falling back to OpenGLES.
I/flutter (21210): [IMPORTANT:flutter/shell/platform/android/android_context_gl_impeller.cc(94)] Using the Impeller rendering backend (OpenGLES).
I/DecorView(21210): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
W/SurfaceView(21210): ViewUI notifySurfaceDestroyed
I/HwViewRootImpl(21210): remove sceneId 1 topId: 0
I/HwViewRootImpl(21210): Add sceneId 10 topId: 0
I/WindowManager(21210): trimMemory level: 20
I/WindowManager(21210): trimMemory level: 40
I/TopResumedActivityChangeItem(21210): execute start, ActivityClientRecord = ActivityRecord{eaedca1 token=android.os.BinderProxy@9383f71 {com.example.legend_dance/com.example.legend_dance.MainActivity}}
W/SurfaceView(21210): ViewUI notifySurfaceDestroyed
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:80:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.367 (+0:00:00.025212)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ --------------------------------------------------------1
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   FileImportService._checkInitialImportFile (package:keepdance/services/file_import_service.dart:51:15)
I/flutter (21210): │ #1   FileImportService._initFileImportListener (package:keepdance/services/file_import_service.dart:41:7)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 开始检查初始导入文件...
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   FileImportService._initFileImportListener (package:keepdance/services/file_import_service.dart:42:15)
I/flutter (21210): │ #1   FileImportService.onInit (package:keepdance/services/file_import_service.dart:32:5)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 💡 文件导入监听器初始化成功
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:90:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.490 (+0:00:00.148568)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ --------------------------------------------------------12222222
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   PosePluginManager._shouldReinitialize (package:keepdance/pose/utils/pose_plugin_manager.dart:199:15)
I/flutter (21210): │ #1   PosePluginManager.initialize (package:keepdance/pose/utils/pose_plugin_manager.dart:108:18)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 💡 PosePluginManager: 首次初始化
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   PosePluginManager._performInitialization (package:keepdance/pose/utils/pose_plugin_manager.dart:130:15)
I/flutter (21210): │ #1   PosePluginManager.initialize (package:keepdance/pose/utils/pose_plugin_manager.dart:115:13)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 💡 PosePluginManager: 开始智能初始化 (maxPoses: 1)
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   PosePlugin.initialize (package:keepdance/pose/utils/pose_plugin.dart:62:15)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.509 (+0:00:00.167646)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 💡 姿势检测插件: 开始初始化检测器, maxPoses: 1
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────
W/SurfaceView(21210): ViewUI notifySurfaceDestroyed
D/OpenGLRenderer(21210): disableOutlineDraw is true
I/TopResumedActivityChangeItem(21210): execute start, ActivityClientRecord = ActivityRecord{eaedca1 token=android.os.BinderProxy@9383f71 {com.example.legend_dance/com.example.legend_dance.MainActivity}}
I/HwViewRootImpl(21210): remove sceneId 10 topId: 0
W/SurfaceView(21210): ViewUI notifySurfaceDestroyed
W/SurfaceView(21210): ViewUI notifySurfaceDestroyed
W/SurfaceView(21210): ViewUI notifySurfaceDestroyed
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   FileImportService._checkInitialImportFile (package:keepdance/services/file_import_service.dart:70:15)
I/flutter (21210): │ #1   <asynchronous suspension>
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 文件导入功能在当前平台暂不可用，应用将正常运行
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ MissingPluginException(No implementation found for method initialize on channel pose_landmarker_plugin)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ #0   MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:368:7)
I/flutter (21210): │ #1   <asynchronous suspension>
I/flutter (21210): │ #2   PosePlugin.initialize (package:keepdance/pose/utils/pose_plugin.dart:65:7)
I/flutter (21210): │ #3   <asynchronous suspension>
I/flutter (21210): │ #4   PosePluginManager._performInitialization (package:keepdance/pose/utils/pose_plugin_manager.dart:137:9)
I/flutter (21210): │ #5   <asynchronous suspension>
I/flutter (21210): │ #6   PosePluginManager.initialize (package:keepdance/pose/utils/pose_plugin_manager.dart:115:7)
I/flutter (21210): │ #7   <asynchronous suspension>
I/flutter (21210): │ #8   CustomApp.main (package:keepdance/main.dart:96:29)
I/flutter (21210): │ #9   <asynchronous suspension>
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.607 (+0:00:00.265997)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ 姿势检测插件: 检测器初始化失败: MissingPluginException(No implementation found for method initialize on channel pose_landmarker_plugin)
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   PosePluginManager._performInitialization (package:keepdance/pose/utils/pose_plugin_manager.dart:165:15)
I/flutter (21210): │ #1   <asynchronous suspension>
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 姿势检测插件在当前平台暂不可用，跳过初始化
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:100:16)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.615 (+0:00:00.274080)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ 姿势检测插件初始化失败: Null check operator used on a null value
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:105:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.620 (+0:00:00.278342)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ --------------------------------------------------------8888888
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:109:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.623 (+0:00:00.281251)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ --------------------------------------------------------233333
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:121:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.624 (+0:00:00.283090)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ --------------------------------------------------------3
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:139:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.626 (+0:00:00.284850)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ -------------------------------------------------------4
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:159:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.741 (+0:00:00.399917)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ --------------------------------------------------------5
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:292:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.750 (+0:00:00.408287)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 开始初始化服务...
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   GlobalImportStateManager.onInit (package:keepdance/services/global_import_state_manager.dart:196:13)
I/flutter (21210): │ #1   GetLifeCycleBase._onStart (package:get/get_instance/src/lifecycle.dart:66:5)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 💡 全局导入状态管理器已初始化
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:303:16)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.772 (+0:00:00.430329)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 GlobalImportStateManager 初始化完成
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:308:16)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.775 (+0:00:00.433726)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 VideoImportRecordService 初始化完成
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:313:16)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.803 (+0:00:00.461329)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 LocalDatabaseService 初始化完成
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:318:16)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.808 (+0:00:00.466903)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 LocalVideoScoreService 初始化完成
Syncing files to device ALN AL00...                                148ms
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:323:16)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.812 (+0:00:00.470832)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 🐛 ImageOptimizationService 初始化完成
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.initDependencies (package:keepdance/main.dart:345:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.818 (+0:00:00.476834)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ 服务初始化失败: "LocalStorageService" not found. You need to call "Get.put(LocalStorageService())" or "Get.lazyPut(()=>LocalStorageService())"
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/ZrHung.AppEyeUiProbe(21210): not watching, wait.
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   MainController.onInit (package:keepdance/controllers/main_controller.dart:19:13)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.824 (+0:00:00.482338)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 💡 MainController - 初始化完成
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (21210): │ #0   CustomApp.main (package:keepdance/main.dart:266:14)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ 16:37:46.827 (+0:00:00.485415)
I/flutter (21210): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (21210): │ ⛔ 应用初始化失败: "CreationPreloadService" not found. You need to call "Get.put(CreationPreloadService())" or "Get.lazyPut(()=>CreationPreloadService())"
I/flutter (21210): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────

Flutter run key commands.
r Hot reload.
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).